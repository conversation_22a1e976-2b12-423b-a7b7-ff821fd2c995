import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/explorer/explorer_screen.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AppItemCard extends StatelessWidget {
  final AppItemData app;
  final VoidCallback onTap;

  const AppItemCard({super.key, required this.app, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Container(
      padding: const EdgeInsets.only(left: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App Icon
          Container(
            width: 92,
            height: 92,
            decoration: BoxDecoration(
              color: themeData.neutral100,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: themeData.neutral200, width: 1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: _buildAppIcon(themeData),
            ),
          ),

          const SizedBox(width: 16),

          // App Info and Button
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App Title and Archive Icon
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        app.title,
                        style: titleMedium.copyWith(
                          color: themeData.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SvgPicture.asset(
                      Assets.icons.icSaveAdd.path,
                      width: 24,
                      height: 24,
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 2),

                // App Description
                Text(
                  app.description,
                  style: bodyMedium.copyWith(color: themeData.textSecondary),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 8),

                // Open Button
                Align(
                  alignment: Alignment.centerRight,
                  child: TSButton(
                    title: 'Open',
                    onPressed: onTap,
                    size: ButtonSize.small,
                    elevatedVariant: ButtonVariant.secondary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Widget _buildAppIcon(ThemeData themeData) {
    // Try to load the SVG icon, fallback to a placeholder if not found
    try {
      return SvgPicture.asset(
        app.iconAsset,
        width: 92,
        height: 92,
        fit: BoxFit.cover,
        placeholderBuilder: (context) => _buildPlaceholderIcon(themeData),
      );
    } catch (e) {
      return _buildPlaceholderIcon(themeData);
    }
  }

  Widget _buildPlaceholderIcon(ThemeData themeData) {
    return Container(
      width: 92,
      height: 92,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            themeData.primaryGreen500.withValues(alpha: 0.8),
            themeData.primaryGreen600.withValues(alpha: 0.6),
          ],
        ),
      ),
      child: Center(
        child: Text(
          app.title.isNotEmpty ? app.title[0].toUpperCase() : '?',
          style: headlineMedium.copyWith(
            color: themeData.schemesOnPrimary,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}
