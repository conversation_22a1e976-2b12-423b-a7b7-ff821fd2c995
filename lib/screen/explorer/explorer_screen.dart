import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/explorer/widget/app_item_card.dart';
import 'package:toii_social/screen/explorer/widget/category_filter_tabs.dart';
import 'package:toii_social/utils/url_luncher/url_luncher.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class ExplorerScreen extends StatefulWidget {
  const ExplorerScreen({super.key});

  @override
  State<ExplorerScreen> createState() => _ExplorerScreenState();
}

class _ExplorerScreenState extends State<ExplorerScreen> {
  String selectedCategory = 'All';

  final List<String> categories = ['All', 'Gaming', 'Social', 'Finance', 'AI'];

  final List<AppItemData> apps = [
    AppItemData(
      title: 'RunX Ring',
      description: 'RunX Ring, an electronic band that you wear on your',
      iconAsset: Assets.icons.icRunx.path,
      url: 'https://apps.apple.com/us/app/runx-ring/id6738332683',
      category: 'Gaming',
    ),
    AppItemData(
      title: 'Toii Network',
      description: 'Decentralized social network built on blockchain',
      iconAsset: Assets.icons.icToii.path,
      url: 'https://toii.xyz',
      category: 'Social',
    ),
    AppItemData(
      title: 'Luna Travel',
      description: 'Your ultimate travel companion for exploring the world',
      iconAsset: Assets.icons.icLunaTravel.path,
      url: 'https://luna.travel',
      category: 'All',
    ),
    AppItemData(
      title: 'Pay Network',
      description: 'Secure and fast payment solutions for everyone',
      iconAsset: Assets.icons.icPayNetwork.path,
      url: 'https://paynetwork.io',
      category: 'Finance',
    ),
    AppItemData(
      title: 'Toii AI',
      description: 'Advanced AI solutions for modern applications',
      iconAsset: Assets.icons.icToiiAi.path,
      url: 'https://toii.ai',
      category: 'AI',
    ),
    AppItemData(
      title: 'NFT Passport',
      description: 'Your digital identity in the metaverse',
      iconAsset: Assets.icons.icNftPassport.path,
      url: 'https://apps.apple.com/us/app/nft-passport/id6736635382',
      category: 'Finance',
    ),
  ];

  List<AppItemData> get filteredApps {
    if (selectedCategory == 'All') {
      return apps;
    }
    return apps.where((app) => app.category == selectedCategory).toList();
  }

  void _onCategorySelected(String category) {
    setState(() {
      selectedCategory = category;
    });
  }

  void _onAppTap(AppItemData app) {
    launchUrlEnhanced(app.url);
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.schemesOnPrimary,
      body: SafeArea(
        child: Column(
          children: [
            // Header Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Explore',
                    style: headlineSmall.copyWith(
                      color: themeData.textPrimary,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        Assets.icons.icSearch.path,
                        width: 24,
                        height: 24,
                        colorFilter: ColorFilter.mode(
                          themeData.textPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Category Filter Tabs
            CategoryFilterTabs(
              categories: categories,
              selectedCategory: selectedCategory,
              onCategorySelected: _onCategorySelected,
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),

                    // Marketplace Section Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Marketplace',
                          style: titleLarge.copyWith(
                            color: themeData.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SvgPicture.asset(
                          Assets.icons.icArrowRight.path,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Marketplace Grid (Placeholder)
                    SizedBox(
                      height: 200,
                      child: GridView.builder(
                        scrollDirection: Axis.horizontal,
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 1,
                              mainAxisSpacing: 8,
                              childAspectRatio: 200 / 143,
                            ),
                        itemCount: 3,
                        itemBuilder: (context, index) {
                          return Container(
                            width: 143,
                            decoration: BoxDecoration(
                              color: themeData.neutral100,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Column(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: themeData.primaryGreen200,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(16),
                                        topRight: Radius.circular(16),
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8),
                                  child: Column(
                                    children: [
                                      Text(
                                        'Marketplace Item',
                                        style: titleMedium.copyWith(
                                          color: themeData.textPrimary,
                                        ),
                                      ),
                                      Text(
                                        '0.36 Toii',
                                        style: titleMedium.copyWith(
                                          color: themeData.primaryGreen500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 32),

                    // DApp & Miniapp Section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Dapp & Miniapp',
                          style: titleLarge.copyWith(
                            color: themeData.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SvgPicture.asset(
                          Assets.icons.icArrowRight.path,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // App List
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: filteredApps.length,
                      separatorBuilder:
                          (context, index) => const SizedBox(height: 20),
                      itemBuilder: (context, index) {
                        final app = filteredApps[index];
                        return AppItemCard(
                          app: app,
                          onTap: () => _onAppTap(app),
                        );
                      },
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AppItemData {
  final String title;
  final String description;
  final String iconAsset;
  final String url;
  final String category;

  const AppItemData({
    required this.title,
    required this.description,
    required this.iconAsset,
    required this.url,
    required this.category,
  });
}
