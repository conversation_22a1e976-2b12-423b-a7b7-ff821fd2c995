import 'package:url_launcher/url_launcher.dart';

void launchUrlCustom(String url) async {
  try {
    await launchUrl(Uri.parse(url), mode: LaunchMode.inAppWebView);
  } catch (e) {}
}

/// Enhanced URL launcher that handles different types of URLs appropriately
void launchUrlEnhanced(String url) async {
  try {
    final uri = Uri.parse(url);

    // App Store links should open externally
    if (url.contains('apps.apple.com') || url.contains('play.google.com')) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      // Web links can open in-app
      await launchUrl(uri, mode: LaunchMode.inAppWebView);
    }
  } catch (e) {
    // Fallback to external browser if in-app fails
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      // Silent fail
    }
  }
}
